"use client";

import { usePathname } from "next/navigation";
import { cn } from "@/lib/utils";

interface SidebarProps {}

export function Sidebar({}: SidebarProps = {}) {
  const pathname = usePathname();

  const menuItems = [
    { label: "Dashboard", href: "/dashboard" },
    { label: "Subscribers", href: "/subscribers" },
    { label: "LE Users", href: "/leUsers" },
    { label: "Content", href: "/content" },
    { label: "Finance", href: "/finance" },
    { label: "Sponsor & ads", href: "/sponsorAds" },
    { label: "Results", href: "/results" },
    { label: "Audit Log", href: "/auditLog" },
    { label: "Master Data", href: "/masterData" },
  ];

  return (
    <div className="relative flex flex-col bg-[#080F17] w-64 border-r border-zinc-800 transition-all duration-300 ease-in-out mt-14">
      <nav className="flex-1 p-4 space-y-2">
        {menuItems.map((item) => {
          const isActive =
            item.href === "/"
              ? pathname === "/"
              : pathname?.startsWith(item.href);

          return (
            <a
              key={item.href}
              href={item.href}
              className={cn(
                "block px-3 py-2 rounded-lg text-sm font-medium transition-colors",
                isActive
                  ? "bg-[#52C3C5]/10 text-[#52C3C5] font-semibold"
                  : "text-gray-400 hover:bg-zinc-800 hover:text-white"
              )}
            >
              {item.label}
            </a>
          );
        })}
      </nav>
    </div>
  );
}
