"use client";

import type React from "react";

import { useState, useMemo } from "react";
import { Card } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { ChevronUp, ChevronDown } from "lucide-react";
import { cn } from "@/lib/utils";

export interface Column<T> {
  key: keyof T | "actions"; // allow "actions"
  label: string;
  sortable?: boolean;
  render?: (value: any, row: T) => React.ReactNode;
  className?: string;
}

interface DynamicDataTableProps<T> {
  data: T[];
  columns: Column<T>[];
  onRowClick?: (row: T) => void;
  className?: string;
  renderActions?: (row: T) => React.ReactNode; // 👈 new
}

type SortDirection = "asc" | "desc" | null;

export function DynamicDataTable<T extends Record<string, any>>({
  data,
  columns,
  onRowClick,
  renderActions,
  className,
}: DynamicDataTableProps<T>) {
  const [sortColumn, setSortColumn] = useState<keyof T | null>(null);
  const [sortDirection, setSortDirection] = useState<SortDirection>(null);

  const sortedData = useMemo(() => {
    if (!sortColumn || !sortDirection) return data;

    return [...data].sort((a, b) => {
      const aValue = a[sortColumn];
      const bValue = b[sortColumn];

      if (aValue < bValue) return sortDirection === "asc" ? -1 : 1;
      if (aValue > bValue) return sortDirection === "asc" ? 1 : -1;
      return 0;
    });
  }, [data, sortColumn, sortDirection]);

  const handleSort = (column: keyof T) => {
    if (sortColumn === column) {
      setSortDirection(
        sortDirection === "asc"
          ? "desc"
          : sortDirection === "desc"
          ? null
          : "asc"
      );
      if (sortDirection === "desc") {
        setSortColumn(null);
      }
    } else {
      setSortColumn(column);
      setSortDirection("asc");
    }
  };

  const renderSortIcon = (column: keyof T) => {
    if (sortColumn !== column) return null;

    return sortDirection === "asc" ? (
      <ChevronUp className="h-4 w-4" />
    ) : (
      <ChevronDown className="h-4 w-4" />
    );
  };

  return (
    <Card
      className={cn(
        "bg-[#171717] backdrop-blur-md border-[#D6DDE6]/20",
        className
      )}
    >
      <div className="overflow-x-auto">
        <table className="w-full">
          <thead>
            <tr>
              {columns.map((column) => (
                <th
                  key={String(column.key)}
                  className={cn(
                    "px-6 py-4 text-left text-sm font-medium text-gray-300 relative", // make it relative
                    column.sortable &&
                      "cursor-pointer hover:text-white transition-colors",
                    column.className
                  )}
                  onClick={() => column.sortable && handleSort(column.key)}
                >
                  <div className="flex items-center gap-2">
                    {column.label}
                    {column.sortable && renderSortIcon(column.key)}
                  </div>

                  {/* short underline */}
                  <span className="absolute bottom-0 left-0 w-5/6 border-b border-[#D6DDE6]/40"></span>
                </th>
              ))}
            </tr>
          </thead>
          <tbody>
            {sortedData.map((row, index) => (
              <tr
                key={index}
                className={cn(
                  "transition-colors",
                  onRowClick && "cursor-pointer hover:bg-white/5"
                )}
                onClick={() => onRowClick?.(row)}
              >
                {columns.map((column) => (
                  <td
                    key={String(column.key)}
                    className={cn(
                      "px-6 py-4 text-sm text-white relative",
                      column.className
                    )}
                  >
                    {column.key === "actions" && renderActions
                      ? renderActions(row) // 👈 actions from parent
                      : column.render
                      ? column.render(row[column.key], row)
                      : String(row[column.key])}

                    <span className="absolute bottom-0 left-0 w-5/6 border-b border-[#D6DDE6]/20"></span>
                  </td>
                ))}
              </tr>
            ))}
          </tbody>
        </table>
      </div>
    </Card>
  );
}

// Status badge component for reuse
export function StatusBadge({ status }: { status: string }) {
  const getStatusColor = (status: string) => {
    switch (status.toLowerCase()) {
      case "done":
      case "completed":
      case "success":
        return "bg-green-500/20 text-green-400 border-green-500/30";
      case "pending":
      case "processing":
        return "bg-orange-500/20 text-orange-400 border-orange-500/30";
      case "cancelled":
      case "failed":
      case "error":
        return "bg-red-500/20 text-red-400 border-red-500/30";
      default:
        return "bg-gray-500/20 text-gray-400 border-gray-500/30";
    }
  };

  return (
    <Badge variant="secondary" className={getStatusColor(status)}>
      <div
        className={cn(
          "w-2 h-2 rounded-full mr-2",
          status.toLowerCase() === "done" && "bg-green-400",
          status.toLowerCase() === "pending" && "bg-orange-400",
          status.toLowerCase() === "cancelled" && "bg-red-400"
        )}
      />
      {status}
    </Badge>
  );
}
