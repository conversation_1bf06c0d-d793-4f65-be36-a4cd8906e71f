"use client";

import React, { useState } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group";
import { Card, CardContent } from "@/components/ui/card";
import { Edit, Trash2, ImageIcon } from "lucide-react";

const TrackManagementPage = () => {
  const [trackName, setTrackName] = useState("");
  const [examEligibility, setExamEligibility] = useState("yes");
  const [selectedImage, setSelectedImage] = useState<string | null>(null);

  // Sample existing tracks data
  const [tracks, setTracks] = useState([
    {
      id: 1,
      name: "Technology",
      image:
        "https://images.unsplash.com/photo-1518709268805-4e9042af2176?w=400&h=250&fit=crop",
      examEligible: true,
    },
    {
      id: 2,
      name: "India",
      image:
        "https://images.unsplash.com/photo-1524492412937-b28074a5d7da?w=400&h=250&fit=crop",
      examEligible: true,
    },
    {
      id: 3,
      name: "World",
      image:
        "https://images.unsplash.com/photo-1614730321146-b6fa6a46bcb4?w=400&h=250&fit=crop",
      examEligible: true,
    },
    {
      id: 4,
      name: "Fundamentals",
      image:
        "https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=400&h=250&fit=crop",
      examEligible: false,
    },
  ]);

  const handleImageUpload = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (file) {
      const reader = new FileReader();
      reader.onload = (e) => {
        if (e.target && typeof e.target.result === "string") {
          setSelectedImage(e.target.result);
        }
      };
      reader.readAsDataURL(file);
    }
  };

  const handleAddTrack = () => {
    if (trackName.trim()) {
      const newTrack = {
        id: tracks.length + 1,
        name: trackName,
        image:
          selectedImage ||
          "https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=400&h=250&fit=crop",
        examEligible: examEligibility === "yes",
      };
      setTracks([...tracks, newTrack]);
      setTrackName("");
      setSelectedImage(null);
      setExamEligibility("yes");
    }
  };

  const handleDeleteTrack = (id: number) => {
    setTracks(tracks.filter((track) => track.id !== id));
  };

  const handleEditTrack = (id: number) => {
    // In a real app, this would open an edit modal or navigate to edit page
    console.log("Edit track with id:", id);
  };

  return (
    <div className="text-white mt-4">
      <div className=" mx-auto space-y-12">
        {/* Add New Track Section */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
          {/* Left: Form Section */}
          <div className="space-y-6">
            {/* Track Name */}
            <div>
              <Label
                htmlFor="trackName"
                className="text-white text-lg font-medium mb-2 block"
              >
                Track Name
              </Label>
              <Input
                id="trackName"
                placeholder="Enter Track name"
                value={trackName}
                onChange={(e) => setTrackName(e.target.value)}
                className="bg-white/4 border-gray-700 text-white placeholder-gray-400 h-12 text-base"
              />
            </div>
            <div>
              <Label className="text-white text-lg font-medium mb-3 block">
                Exam Eligibility
              </Label>
              <RadioGroup
                value={examEligibility}
                onValueChange={setExamEligibility}
                className="flex gap-6"
              >
                <div className="flex items-center space-x-2">
                  <RadioGroupItem
                    value="yes"
                    id="yes"
                    className="border-gray-400 text-white"
                  />
                  <Label htmlFor="yes" className="text-white text-base">
                    Yes
                  </Label>
                </div>
                <div className="flex items-center space-x-2">
                  <RadioGroupItem
                    value="no"
                    id="no"
                    className="border-gray-400 text-white"
                  />
                  <Label htmlFor="no" className="text-white text-base">
                    No
                  </Label>
                </div>
              </RadioGroup>
            </div>
          </div>
          <div className="space-y-4">
            <div className="relative">
              <input
                type="file"
                accept="image/*"
                onChange={handleImageUpload}
                className="absolute inset-0 opacity-0 cursor-pointer"
                id="image-upload"
              />
              <Card className="bg-white/4 border-gray-700 h-64">
                <CardContent className="p-0 h-full">
                  {selectedImage ? (
                    <div className="relative h-full">
                      <img
                        src={selectedImage}
                        alt="Preview"
                        className="w-full h-full object-cover rounded-lg"
                      />
                      <Button
                        size="sm"
                        variant="secondary"
                        className="absolute top-3 right-3 bg-gray-700 hover:bg-gray-600"
                        onClick={() =>
                          document.getElementById("image-upload")?.click()
                        }
                      >
                        <Edit className="h-4 w-4 mr-1" />
                        Edit
                      </Button>
                    </div>
                  ) : (
                    <label
                      htmlFor="image-upload"
                      className="flex flex-col items-center justify-center h-full cursor-pointer hover:bg-gray-700 rounded-lg transition-colors"
                    >
                      <div className="p-4 bg-gray-700 rounded-lg mb-3">
                        <ImageIcon className="h-8 w-8 text-gray-300" />
                      </div>
                      <span className="text-gray-400 text-sm">
                        Click to upload image
                      </span>
                    </label>
                  )}
                </CardContent>
              </Card>
            </div>

            {/* Action Buttons */}
            <div className="flex justify-end space-x-3">
              <Button
                variant="outline"
                className="border-gray-600 text-gray-300 hover:bg-gray-800"
                onClick={() => {
                  setTrackName("");
                  setSelectedImage(null);
                  setExamEligibility("yes");
                }}
              >
                Cancel
              </Button>
              <Button
                onClick={handleAddTrack}
                className="bg-green-600 hover:bg-green-700 text-white"
                disabled={!trackName.trim()}
              >
                Add
              </Button>
            </div>
          </div>
        </div>
        <div className="space-y-1">
          <div>
            <h2 className="text-xl font-semibold text-white mb-2">
              Existing Tracks
            </h2>
            <p className="text-gray-400 text-sm">
              Manage and monitor all existing tracks
            </p>
          </div>

          <div className="grid gap-4">
            {tracks.map((track) => (
              <Card
                key={track.id}
                className="bg-white/4 border-gray-700 overflow-hidden"
              >
                <CardContent>
                  <div className="relative h-36 w-full flex">
                    {/* Left side - Image & Title */}
                    <div className="w-1/2 h-full p-1 relative rounded-xl">
                      <img
                        src={track.image}
                        alt={track.name}
                        className="w-full h-full rounded-xl"
                      />
                      <h3 className="absolute inset-0 flex items-center justify-center text-2xl font-bold text-white">
                        {track.name}
                      </h3>
                    </div>

                    {/* Right side - Actions */}
                    <div className="w-1/2 h-full flex items-center justify-center pr-4">
                      <div className="flex flex-col items-start space-y-2">
                        {track.examEligible && (
                          <span className="bg-primary text-black font-medium px-3 py-1 rounded-md text-sm">
                            Exam Eligible
                          </span>
                        )}
                        <div className="flex space-x-2">
                          <Button
                            size="sm"
                            variant="secondary"
                            onClick={() => handleEditTrack(track.id)}
                            className="bg-gray-200 hover:bg-gray-300 text-black"
                          >
                            <Edit className="h-4 w-4 mr-1" />
                            Edit
                          </Button>
                          <Button
                            size="sm"
                            variant="destructive"
                            onClick={() => handleDeleteTrack(track.id)}
                            className="bg-[#D1E4EB]/64 hover:bg-[#D1E4EB]/30 text-[#CD0035]"
                          >
                            <Trash2 className="h-4 w-4 mr-1" />
                            Delete
                          </Button>
                        </div>
                      </div>
                    </div>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        </div>
      </div>
    </div>
  );
};

export default TrackManagementPage;
