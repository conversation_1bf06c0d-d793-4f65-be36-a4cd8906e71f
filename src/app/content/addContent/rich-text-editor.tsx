"use client";

import { useState, useEffect, useRef } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Alert, AlertDescription } from "@/components/ui/alert";
import {
  Bold,
  Italic,
  Underline,
  AlignLeft,
  AlignCenter,
  AlignRight,
  List,
  ListOrdered,
  LinkIcon,
  ImageIcon,
  ChevronLeft,
  ChevronRight,
  FileText,
  Plus,
  AlertTriangle,
  Trash,
} from "lucide-react";
import FilterSelect from "@/components/constant/filter-select";

interface PageContent {
  id: string;
  content: string;
  title: string;
}

const planOption = [
  { value: "textOnly", label: "Text Only" },
  { value: "TextAndImage", label: "Text and Image" },
  { value: "ImageOnly", label: "Image Only" },
  { value: "Video", label: "Video" },
];

export function RichTextEditor() {
  const [pages, setPages] = useState<PageContent[]>([
    { id: "1", content: "", title: "Page 1" },
  ]);
  const [currentPageIndex, setCurrentPageIndex] = useState(0);
  const [maxWordsPerPage, setMaxWordsPerPage] = useState(500);
  const [showPageSettings] = useState(false);
  const [typeFilter, setTypeFilter] = useState("textOnly");

  const currentPage = pages[currentPageIndex];
  const wordCount =
    currentPage?.content
      .replace(/<[^>]*>/g, "")
      .split(/\s+/)
      .filter((w) => w.length > 0).length || 0;
  const isPageFull = wordCount >= maxWordsPerPage;
  const isNearLimit = wordCount >= maxWordsPerPage * 0.8;

  useEffect(() => {
    if (isPageFull && pages.length === currentPageIndex + 1) {
      addNewPage();
    }
  }, [wordCount, maxWordsPerPage]);

  const formatText = (command: string, value?: string) => {
    document.execCommand(command, false, value);
  };

  const insertImage = () => {
    const url = prompt("Enter image URL:");
    if (url) document.execCommand("insertImage", false, url);
  };

  const insertLink = () => {
    const url = prompt("Enter link URL:");
    if (url) document.execCommand("createLink", false, url);
  };

  const addNewPage = () => {
    const newPage: PageContent = {
      id: Date.now().toString(),
      content: "",
      title: `Page ${pages.length + 1}`,
    };
    setPages([...pages, newPage]);
    setCurrentPageIndex(pages.length);
  };

  const deleteCurrentPage = () => {
    if (pages.length > 1) {
      const updatedPages = pages.filter((_, i) => i !== currentPageIndex);
      setPages(updatedPages);
      setCurrentPageIndex(Math.max(0, currentPageIndex - 1));
    }
  };

  const updateCurrentPageContent = (content: string) => {
    setPages((prev) => {
      const updated = [...prev];
      updated[currentPageIndex] = {
        ...updated[currentPageIndex],
        content,
      };
      return updated;
    });
  };

  return (
    <div className="space-y-4">
      {/* Navigation */}
      <Card className="bg-white/4">
        <CardHeader>
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-3">
              <FileText className="h-5 w-5 text-white" />
              <div className="text-white">
                <h3 className="font-semibold">
                  {currentPage?.title} of {pages.length}
                </h3>
                <p className="text-sm">Java Basics - Introduction</p>
              </div>
            </div>
            <div className="flex items-center gap-2">
              <Button
                variant="outline"
                size="sm"
                className="bg-white"
                onClick={() =>
                  setCurrentPageIndex(Math.max(0, currentPageIndex - 1))
                }
                disabled={currentPageIndex === 0}
              >
                <ChevronLeft className="h-4 w-4" />
                Previous
              </Button>
              <Badge variant="secondary">
                {currentPageIndex + 1}/{pages.length}
              </Badge>
              <Button
                variant="outline"
                size="sm"
                className="bg-[#D7E1E4]"
                onClick={() =>
                  setCurrentPageIndex(
                    Math.min(pages.length - 1, currentPageIndex + 1)
                  )
                }
                disabled={currentPageIndex === pages.length - 1}
              >
                Next
                <ChevronRight className="h-4 w-4" />
              </Button>
            </div>
          </div>
        </CardHeader>
      </Card>

      <Card className="bg-white/4 text-white">
        <CardHeader>
          <div className="flex items-center justify-between">
            <CardTitle>Page Management</CardTitle>
            <div className="flex gap-2">
              <Button
                variant="outline"
                className="bg-[#016E01] border border-[#016E01]/60 hover:bg-[#016E01]/80 text-white"
                size="sm"
                onClick={addNewPage}
              >
                <Plus className="h-4 w-4 mr-1" />
                Add Page
              </Button>
              <Button
                size="sm"
                className="bg-[#D1E4EB]/62 border text-bold border-[#D1E4EB]/20 hover:bg-[#D1E4EB]/40 text-[#CD0035]"
                onClick={deleteCurrentPage}
                disabled={pages.length <= 1}
              >
                <Trash className="h-4 w-4 mr-1" />
                Delete Page
              </Button>
            </div>
          </div>
          {showPageSettings && (
            <div className="mt-4 p-4 border rounded-lg bg-muted">
              <div className="flex items-center gap-4">
                <label className="text-sm font-medium">
                  Max words per page:
                </label>
                <input
                  type="number"
                  value={maxWordsPerPage}
                  onChange={(e) => setMaxWordsPerPage(Number(e.target.value))}
                  className="w-20 px-2 py-1 border rounded text-sm"
                  min="100"
                  max="2000"
                />
                <span className="text-sm text-muted-foreground">
                  Current: {wordCount} words
                </span>
              </div>
            </div>
          )}
        </CardHeader>
      </Card>

      {/* Warning */}
      {isNearLimit && (
        <Alert
          className={
            isPageFull
              ? "border-red-200 bg-red-50"
              : "border-yellow-200 bg-yellow-50"
          }
        >
          <AlertTriangle className="h-4 w-4" />
          <AlertDescription>
            {isPageFull
              ? `Page is full (${wordCount}/${maxWordsPerPage} words).`
              : `Page is getting long (${wordCount}/${maxWordsPerPage} words).`}
          </AlertDescription>
        </Alert>
      )}
      <div className="flex flex-col gap-2 w-1/2">
        <h2 className="text-lg font-semibold text-white">Enter Template</h2>
        <FilterSelect
          placeholder="Page Types"
          value={typeFilter}
          onChange={setTypeFilter}
          options={planOption}
        />
      </div>

      {/* Editor */}
      <Card className="bg-white/4 text-white">
        <CardHeader>
          <CardTitle>{currentPage?.title}</CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          {/* Toolbar */}
          <div className="flex flex-wrap gap-2 p-2 border rounded-lg bg-white/20">
            <Button
              variant="ghost"
              size="sm"
              onClick={() => formatText("bold")}
            >
              <Bold className="h-4 w-4" />
            </Button>
            <Button
              variant="ghost"
              size="sm"
              onClick={() => formatText("italic")}
            >
              <Italic className="h-4 w-4" />
            </Button>
            <Button
              variant="ghost"
              size="sm"
              onClick={() => formatText("underline")}
            >
              <Underline className="h-4 w-4" />
            </Button>
            <Button
              variant="ghost"
              size="sm"
              onClick={() => formatText("justifyLeft")}
            >
              <AlignLeft className="h-4 w-4" />
            </Button>
            <Button
              variant="ghost"
              size="sm"
              onClick={() => formatText("justifyCenter")}
            >
              <AlignCenter className="h-4 w-4" />
            </Button>
            <Button
              variant="ghost"
              size="sm"
              onClick={() => formatText("justifyRight")}
            >
              <AlignRight className="h-4 w-4" />
            </Button>
            <Button
              variant="ghost"
              size="sm"
              onClick={() => formatText("insertUnorderedList")}
            >
              <List className="h-4 w-4" />
            </Button>
            <Button
              variant="ghost"
              size="sm"
              onClick={() => formatText("insertOrderedList")}
            >
              <ListOrdered className="h-4 w-4" />
            </Button>
            <Button variant="ghost" size="sm" onClick={insertLink}>
              <LinkIcon className="h-4 w-4" />
            </Button>
            <Button variant="ghost" size="sm" onClick={insertImage}>
              <ImageIcon className="h-4 w-4" />
            </Button>
          </div>

            <div
              className="min-h-[400px] p-4 border rounded-lg bg-white/20 focus:outline-none focus:ring-2 focus:ring-ring"
              contentEditable
              suppressContentEditableWarning={true} // Prevent React warning
              ref={(el) => {
                if (el && el.innerHTML !== currentPage?.content) {
                  // Set only when page changes, NOT every keystroke
                  el.innerHTML = currentPage?.content || "";
                }
              }}
              onInput={(e) => {
                const html = e.currentTarget.innerHTML;
                updateCurrentPageContent(html);
              }}
              style={{ minHeight: "400px" }}
            />

          {/* Stats */}
          <div className="flex justify-between text-sm text-muted-foreground">
            <span>
              Characters: {currentPage?.content.replace(/<[^>]*>/g, "").length}
            </span>
            <span>Words: {wordCount}</span>
            <span>
              Limit:{" "}
              <span
                className={
                  wordCount >= maxWordsPerPage ? "text-red-600 font-bold" : ""
                }
              >
                {wordCount}/{maxWordsPerPage}
              </span>
            </span>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
