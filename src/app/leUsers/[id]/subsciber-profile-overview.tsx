"use client";

import { InfoCard } from "@/components/constant/info-card";
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, CardContent } from "@/components/ui/card";
import { FileText } from "lucide-react";
import React from "react";

interface TeamMember {
  id: string;
  name: string;
  email: string;
  phone: string;
  avatar?: string;
  joinedDate: string;
  lastActive: string;
  status: "Active" | "Inactive";
  plan: "Pro" | "Basic" | "Premium";
  role?: string;
  mobileNumber?: string;
}

interface Props {
  teamMember: TeamMember;
}

export default function SubscriberProfileOverview({ teamMember }: Props) {
  return (
    <div className="grid grid-cols-1 md:grid-cols-3 gap-6 p-6 w-full">
      <InfoCard
        title="Account Summary"
        items={[
          {
            label: "User ID",
            value: <span className="font-mono">{teamMember.id}</span>,
          },
          { label: "Credits", value: "150" },
          { label: "Total Spend", value: "₹ 625" },
          {
            label: "Pan no",
            value: <span className="font-mono">APJ3USAQ</span>,
          },
        ]}
      />

      <InfoCard
        title="Contact Information"
        icon={<FileText className="h-4 w-4 text-zinc-400" />}
        items={[
          { label: "Mobile Number", value: teamMember.mobileNumber || "N/A" },
          { label: "State", value: "Kerala" },
          { label: "City", value: "Kollam" },
          { label: "Pincode", value: "695024" },
        ]}
      />

      <InfoCard
        title="Subscription Details"
        items={[
          { label: "Current Plan", value: teamMember.plan },
          { label: "Status", value: teamMember.status },
          {
            label: "Payment method",
            value: <span className="font-mono">UPI</span>,
          },
        ]}
      />
    </div>
  );
}
